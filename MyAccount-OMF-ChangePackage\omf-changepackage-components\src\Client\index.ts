import { Injectable, AjaxServices, CommonFeatures, AjaxOptions } from "bwtk";

import { Observable, Observer } from "bwtk/node_modules/rxjs";
import { Volt, Models } from "../Models";
import { Utils, ValueOf } from "../Utils";

const _isDebug = Boolean(Utils.getCookie("debugwidget"));

function createObservableRequest(method: () => Promise<any>) {
  return Observable.create((observer: Observer<any>) => {
    method()
      .then(response => {
        observer.next(response);
      })
      .catch(error => {
        observer.error(error);
      })
      .then(() => {
        observer.complete();
      });
  });
}

function mockResponder(data: any): Observable<any> {
  return createObservableRequest(() => new Promise<any>((resolve) => {
    setTimeout(() => resolve({ data }), 350);
  }));
}

function cleanPath(path: string): string {
  return (path || "").split("/").pop() as string;
}

/**
 * Base client implementation
* Ajax client wrapper inplementing Graph specific calls
* @export
* @abstract
* @class Client
* @extends {CommonFeatures.BaseClient}
*/
@Injectable
export abstract class BaseClient extends CommonFeatures.BaseClient {
  /**
     * Creates an instance of Client.
     * @param {AjaxServices} ajax
     * @param {Models.IBaseConfig} config
     * @memberof Client
     */
  constructor(ajax: AjaxServices, private config: Models.IBaseConfig) {
    super(ajax);
  }
  private get useMockData(): boolean {
    return (_isDebug && this.config.mockdata !== undefined) ||
            (this.config.mockdata !== undefined && this.config.environmentVariables.useMockData);
  }

  private get mockdata(): { [key: string]: { [key: string]: any } } {
    return this.config.mockdata || {};
  }

  public get<T>(path: string, _data?: any, _options?: AjaxOptions | undefined): Observable<T> {
    const mock = ValueOf(this.mockdata, cleanPath(path) + ".GET", false);
    return this.useMockData &&
            mock ?
      mockResponder(mock)
      : super.get.apply(this, arguments);
  }

  public put<T>(path: string, _data: any, _options?: AjaxOptions | undefined): Observable<T> {
    const mock = ValueOf(this.mockdata, cleanPath(path) + ".PUT", false);
    return this.useMockData &&
            mock ?
      mockResponder(mock)
      : super.put.apply(this, arguments);
  }

  public post<T>(path: string, _data: any, _options?: AjaxOptions | undefined): Observable<T> {
    const mock = ValueOf(this.mockdata, cleanPath(path) + ".POST", false);
    return this.useMockData &&
            mock ?
      mockResponder(mock)
      : super.post.apply(this, arguments);
  }

  public del<T>(path: string, _options?: AjaxOptions | undefined): Observable<T> {
    const mock = ValueOf(this.mockdata, cleanPath(path) + ".DELETE", false);
    return this.useMockData &&
            mock ?
      mockResponder(mock)
      : super.del.apply(this, arguments);
  }

  public action<T>(action: Volt.IHypermediaAction): Observable<T> {
    switch (action.method) {
      case "PUT": return this.put(action.href, action.messageBody);
      case "POST": return this.post(action.href, action.messageBody);
      case "DELETE": return this.del(action.href) as any;
      case "GET":
      default:
        return this.get(action.href, action.messageBody);
    }
  }

  get options() {
    return {
      url: this.config.api.base,
      cache: false,
      credentials: "include" as RequestCredentials,
      headers: this.config.headers
    };
  }
}
