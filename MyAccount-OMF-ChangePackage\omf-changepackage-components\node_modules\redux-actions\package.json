{"name": "redux-actions", "version": "3.0.3", "description": "Flux Standard Action utlities for Redux", "type": "module", "scripts": {"format": "prettier --write \"**/*.{js,md}\"", "format:check": "prettier --list-different \"**/*.{js,md}\"", "build": "vite build", "clean": "rimraf coverage dist es lib", "prepublishOnly": "run-s build", "test": "vitest run --coverage", "lint": "xo"}, "files": ["dist"], "module": "./dist/redux-actions.js", "exports": {".": {"import": "./dist/redux-actions.js", "default": "./dist/redux-actions.js"}}, "keywords": ["flux", "redux", "fsa", "actions"], "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/redux-utilities/redux-actions/issues"}, "homepage": "https://github.com/redux-utilities/redux-actions", "repository": {"type": "git", "url": "https://github.com/redux-utilities/redux-actions.git"}, "license": "MIT", "devDependencies": {"@vitest/coverage-c8": "0.26.2", "eslint-config-unicorn-camelcase": "0.1.1", "flux-standard-action": "2.1.2", "husky": "8.0.2", "lint-staged": "13.1.0", "npm-run-all": "4.1.5", "prettier": "2.8.1", "vite": "4.0.3", "vitest": "0.26.2", "xo": "0.53.1"}, "dependencies": {"just-curry-it": "5.3.0", "reduce-reducers": "1.0.4"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,md}": ["prettier --write", "git add"]}, "xo": {"prettier": true, "space": true, "extends": ["unicorn-camelcase"], "ignores": ["vite.config.js"], "rules": {"import/no-anonymous-default-export": "off", "unicorn/no-array-reduce": "off", "n/file-extension-in-import": "off", "import/extensions": "off", "unicorn/prevent-abbreviations": "off", "unicorn/no-array-for-each": "off", "unicorn/no-for-loop": "off", "padding-line-between-statements": "off", "unicorn/no-array-callback-reference": "off", "n/prefer-global/process": "off", "unicorn/prefer-export-from": "off", "unicorn/prefer-array-flat": "off"}}, "prettier": {"singleQuote": true, "bracketSpacing": true, "trailingComma": "none"}}