{"name": "@formatjs/intl", "description": "Internationalize JS apps. This library provides an API to format dates, numbers, and strings, including pluralization and handling translations.", "version": "3.1.6", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "dependencies": {"tslib": "^2.8.0", "@formatjs/icu-messageformat-parser": "2.11.2", "@formatjs/ecma402-abstract": "2.3.4", "intl-messageformat": "10.7.16", "@formatjs/fast-memoize": "2.2.7"}, "peerDependencies": {"typescript": "^5.6.0"}, "bugs": "https://github.com/formatjs/formatjs/issues", "homepage": "https://formatjs.github.io", "keywords": ["format", "formatting", "globalization", "i18n", "internationalization", "intl", "locale", "localization", "react-intl", "translate", "translation"], "main": "index.js", "module": "lib/index.js", "peerDependenciesMeta": {"typescript": {"optional": true}}, "repository": "**************:formatjs/formatjs.git"}